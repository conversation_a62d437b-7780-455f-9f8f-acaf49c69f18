#!/usr/bin/env tsx

/**
 * Simple migration script to add registration_type column to organizations table
 * and create organization_users table
 */

import { sequelize } from '../app/models/index';

async function addRegistrationType() {
  try {
    console.log('Starting migration...');

    // Check if registration_type column exists in organizations table
    const [results] = await sequelize.query(`
      PRAGMA table_info(organizations);
    `);

    const hasRegistrationType = results.some((column: any) => column.name === 'registration_type');

    if (!hasRegistrationType) {
      console.log('Adding registration_type column to organizations table...');
      await sequelize.query(`
        ALTER TABLE organizations 
        ADD COLUMN registration_type TEXT DEFAULT 'whitelist' CHECK (registration_type IN ('whitelist', 'open'));
      `);
      console.log('✓ Added registration_type column');
    } else {
      console.log('✓ registration_type column already exists');
    }

    // Check if organization_users table exists
    const [tables] = await sequelize.query(`
      SELECT name FROM sqlite_master WHERE type='table' AND name='organization_users';
    `);

    if (tables.length === 0) {
      console.log('Creating organization_users table...');
      await sequelize.query(`
        CREATE TABLE organization_users (
          id TEXT PRIMARY KEY,
          organization_id TEXT NOT NULL,
          user_id TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
          UNIQUE(organization_id, user_id)
        );
      `);

      // Create indexes
      await sequelize.query(`
        CREATE INDEX idx_organization_users_org_id ON organization_users(organization_id);
      `);
      await sequelize.query(`
        CREATE INDEX idx_organization_users_user_id ON organization_users(user_id);
      `);

      console.log('✓ Created organization_users table with indexes');
    } else {
      console.log('✓ organization_users table already exists');
    }

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run migration
addRegistrationType();
