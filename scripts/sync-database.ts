#!/usr/bin/env tsx

/**
 * Database synchronization script
 * This will create/update tables based on the current models
 */

import { sequelize } from '../app/models/index';

async function syncDatabase() {
  try {
    console.log('Starting database synchronization...');
    
    // Sync all models with alter: true to update existing tables
    await sequelize.sync({ alter: true });
    
    console.log('✓ Database synchronized successfully!');
    console.log('All tables have been created/updated according to the current models.');
    
  } catch (error) {
    console.error('Database synchronization failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run synchronization
syncDatabase();
