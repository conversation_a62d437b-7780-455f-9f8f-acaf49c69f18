import { DataTypes, Model, Sequelize, InferAttributes, InferCreationAttributes } from 'sequelize'

export class OrganizationUserInstance extends Model<
  InferAttributes<OrganizationUserInstance>,
  InferCreationAttributes<OrganizationUserInstance>
> {
  declare id: string
  declare organizationId: string
  declare userId: string
  declare createdAt?: Date

  static initialize(sequelize: Sequelize) {
    return this.init(
      {
        id: {
          type: DataTypes.STRING,
          primaryKey: true,
        },
        organizationId: {
          type: DataTypes.STRING,
          allowNull: false,
          field: 'organization_id',
        },
        userId: {
          type: DataTypes.STRING,
          allowNull: false,
          field: 'user_id',
        },
        createdAt: {
          type: DataTypes.DATE,
          field: 'created_at',
          defaultValue: DataTypes.NOW,
        },
      },
      {
        sequelize,
        tableName: 'organization_users',
        timestamps: true,
        createdAt: 'created_at',
        updatedAt: false,
        indexes: [
          {
            unique: true,
            fields: ['organization_id', 'user_id'],
          },
          {
            fields: ['organization_id'],
          },
          {
            fields: ['user_id'],
          },
        ],
      },
    )
  }
}

export function OrganizationUserModel(sequelize: Sequelize) {
  return OrganizationUserInstance.initialize(sequelize)
}
