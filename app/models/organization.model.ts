import { DataTypes, Model, Sequelize, InferAttributes, InferCreationAttributes } from 'sequelize'

export class OrganizationInstance extends Model<
  InferAttributes<OrganizationInstance>,
  InferCreationAttributes<OrganizationInstance>
> {
  declare id: string
  declare name: string
  declare problemSetId: string
  declare registrationType: 'whitelist' | 'open'
  declare createdAt?: Date
  declare updatedAt?: Date

  static initialize(sequelize: Sequelize) {
    return this.init(
      {
        id: {
          type: DataTypes.STRING,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        problemSetId: {
          type: DataTypes.STRING,
          allowNull: false,
          field: 'problem_set_id',
        },
        registrationType: {
          type: DataTypes.ENUM('whitelist', 'open'),
          allowNull: false,
          defaultValue: 'whitelist',
          field: 'registration_type',
        },
        createdAt: {
          type: DataTypes.DATE,
          field: 'created_at',
        },
        updatedAt: {
          type: DataTypes.DATE,
          field: 'updated_at',
        },
      },
      {
        sequelize,
        tableName: 'organizations',
        timestamps: true,
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    )
  }
}

export function OrganizationModel(sequelize: Sequelize) {
  return OrganizationInstance.initialize(sequelize)
} 