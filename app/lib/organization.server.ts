import { Organization, ProblemSet, OrganizationUser, User, Test } from "~/models";
import { v4 as uuidv4 } from "uuid";
import { nanoid } from "nanoid";

export class OrganizationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "OrganizationError";
  }
}

export async function createOrganization(
  name: string,
  problemSetId: string,
  customId?: string,
  registrationType: 'whitelist' | 'open' = 'whitelist'
) {
  // Verify the problem set exists
  const problemSet = await ProblemSet.findByPk(problemSetId);
  if (!problemSet) {
    throw new OrganizationError("Problem set not found");
  }

  // Use custom ID if provided, otherwise generate a UUID
  const id = customId || uuidv4();

  // Check if an organization with this ID already exists
  if (customId) {
    const existingOrg = await Organization.findByPk(customId);
    if (existingOrg) {
      throw new OrganizationError(
        `Organization with ID ${customId} already exists`
      );
    }
  }

  const organization = await Organization.create({
    id,
    name,
    problemSetId,
    registrationType,
  });

  return organization;
}

export async function getOrganization(id: string) {
  const organization = await Organization.findByPk(id);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }
  return organization;
}

export async function updateOrganizationProblemSet(
  id: string,
  problemSetId: string
) {
  // Verify the problem set exists
  const problemSet = await ProblemSet.findByPk(problemSetId);
  if (!problemSet) {
    throw new OrganizationError("Problem set not found");
  }

  const organization = await Organization.findByPk(id);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }

  organization.problemSetId = problemSetId;
  await organization.save();

  return organization;
}

export async function getOrganizationProblemSetId(
  orgId: string
): Promise<string> {
  const organization = await Organization.findByPk(orgId);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }

  return organization.problemSetId;
}

export async function deleteOrganization(id: string) {
  const organization = await Organization.findByPk(id);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }

  await organization.destroy();
  return true;
}

export async function addUserToOrganization(
  organizationId: string,
  userId: string
): Promise<void> {
  // Check if organization exists
  const organization = await Organization.findByPk(organizationId);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }

  // Check if user exists
  const user = await User.findByPk(userId);
  if (!user) {
    throw new OrganizationError("User not found");
  }

  // Check if association already exists
  const existingAssociation = await OrganizationUser.findOne({
    where: {
      organizationId,
      userId,
    },
  });

  if (!existingAssociation) {
    await OrganizationUser.create({
      id: nanoid(),
      organizationId,
      userId,
    });
  }
}

export async function getOrganizationUsers(organizationId: string) {
  const organization = await Organization.findByPk(organizationId);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }

  const organizationUsers = await OrganizationUser.findAll({
    where: { organizationId },
    include: [
      {
        model: User,
        attributes: ['id', 'name', 'email', 'phoneNumber', 'createdAt'],
      },
    ],
    order: [['createdAt', 'DESC']],
  });

  return organizationUsers.map((orgUser) => ({
    id: orgUser.id,
    userId: orgUser.userId,
    joinedAt: orgUser.createdAt,
    user: orgUser.User,
  }));
}

export async function getOrganizationStats(organizationId: string) {
  const organization = await Organization.findByPk(organizationId);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }

  // Get user count
  const userCount = await OrganizationUser.count({
    where: { organizationId },
  });

  // Get test count and details
  const organizationUsers = await OrganizationUser.findAll({
    where: { organizationId },
    attributes: ['userId'],
  });

  const userIds = organizationUsers.map((ou) => ou.userId);

  const tests = await Test.findAll({
    where: {
      userId: userIds,
      problemSetId: organization.problemSetId,
    },
    include: [
      {
        model: User,
        attributes: ['id', 'name', 'email'],
      },
    ],
    order: [['createdAt', 'DESC']],
  });

  return {
    userCount,
    testCount: tests.length,
    tests: tests.map((test) => ({
      id: test.id,
      url: test.url,
      createdAt: test.createdAt,
      user: test.User,
    })),
  };
}
